#!/usr/bin/env python3
"""
批量处理所有PCM文件生成大型训练数据集
"""

import os
import struct
import math
import random

def read_pcm_file(filename):
    """读取PCM文件并返回样本列表"""
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        samples = []
        for i in range(0, len(data), 2):
            if i + 1 < len(data):
                sample = struct.unpack('<h', data[i:i+2])[0]
                normalized = sample / 32768.0
                samples.append(normalized)
        
        return samples
    except Exception as e:
        print(f"读取PCM文件错误 {filename}: {e}")
        return None

def compute_energy(samples):
    """计算信号能量"""
    energy = 0.0
    for sample in samples:
        energy += sample * sample
    return energy / len(samples) if samples else 0.0

def simple_fft_magnitude(samples, num_bands=22):
    """简化的频谱分析"""
    frame_size = len(samples)
    band_size = frame_size // num_bands
    band_energies = []
    
    for band in range(num_bands):
        start_idx = band * band_size
        end_idx = min((band + 1) * band_size, frame_size)
        
        band_energy = 0.0
        for i in range(start_idx, end_idx):
            band_energy += samples[i] * samples[i]
        
        freq_weight = 1.0 + 0.1 * math.sin(band * math.pi / num_bands)
        band_energy *= freq_weight
        
        band_energies.append(band_energy)
    
    return band_energies

def create_87d_features(clean_samples, noise_samples):
    """创建87维特征向量"""
    min_len = min(len(clean_samples), len(noise_samples))
    clean_samples = clean_samples[:min_len]
    noise_samples = noise_samples[:min_len]
    
    # 混合信号
    noise_level = random.uniform(0.1, 0.5)  # 随机噪声水平
    mixed_samples = []
    for i in range(min_len):
        mixed_samples.append(clean_samples[i] + noise_level * noise_samples[i])
    
    # 计算特征
    clean_energy = compute_energy(clean_samples)
    mixed_energy = compute_energy(mixed_samples)
    
    mixed_bands = simple_fft_magnitude(mixed_samples, 22)
    clean_bands = simple_fft_magnitude(clean_samples, 22)
    noise_bands = simple_fft_magnitude(noise_samples, 22)
    
    features = [0.0] * 87
    
    # 前22维：混合信号频带能量
    for i in range(22):
        log_energy = math.log(mixed_bands[i] + 1e-10)
        features[i] = log_energy
    
    # 接下来20维：上下文特征
    for i in range(20):
        if i < 22:
            if i > 0:
                features[22 + i] = features[i] - features[i-1]
            else:
                features[22 + i] = features[i]
        else:
            if i == 18:
                features[22 + i] = mixed_energy / (clean_energy + 1e-10)
            else:
                features[22 + i] = random.uniform(-0.1, 0.1)
    
    # 中间22维：目标增益
    for i in range(22):
        gain = math.sqrt((clean_bands[i] + 1e-10) / (mixed_bands[i] + 1e-10))
        gain = min(gain, 2.0)
        features[42 + i] = gain
    
    # 接下来22维：噪声特征
    for i in range(22):
        log_noise = math.log(noise_bands[i] + 1e-10)
        features[64 + i] = log_noise
    
    # 最后1维：VAD
    vad = 1.0 if clean_energy > 0.01 else 0.0
    features[86] = vad
    
    return features

def process_all_pcm_files(clean_dir, noise_dir, output_file, max_files=50):
    """处理所有PCM文件"""
    
    # 获取所有文件
    clean_files = []
    noise_files = []
    
    for filename in os.listdir(clean_dir):
        if filename.endswith('.pcm'):
            clean_files.append(os.path.join(clean_dir, filename))
    
    for filename in os.listdir(noise_dir):
        if filename.endswith('.pcm'):
            noise_files.append(os.path.join(noise_dir, filename))
    
    clean_files.sort()
    noise_files.sort()
    
    print(f"找到 {len(clean_files)} 个干净语音文件")
    print(f"找到 {len(noise_files)} 个噪声文件")
    print(f"将处理前 {max_files} 个文件")
    
    all_features = []
    frame_size = 480  # 10ms at 48kHz
    frames_per_file = 100  # 每个文件处理更多帧
    
    processed_count = 0
    
    for i in range(min(len(clean_files), max_files)):
        for j in range(min(len(noise_files), 3)):  # 每个语音文件与3个不同噪声文件配对
            clean_file = clean_files[i]
            noise_file = noise_files[(i + j) % len(noise_files)]  # 循环使用噪声文件
            
            processed_count += 1
            print(f"处理文件对 {processed_count}: {os.path.basename(clean_file)} + {os.path.basename(noise_file)}")
            
            # 读取音频
            clean_samples = read_pcm_file(clean_file)
            noise_samples = read_pcm_file(noise_file)
            
            if clean_samples is None or noise_samples is None:
                continue
            
            # 处理多个帧
            min_length = min(len(clean_samples), len(noise_samples))
            max_frames = min(frames_per_file, (min_length - frame_size) // frame_size)
            
            for frame_idx in range(max_frames):
                start_idx = frame_idx * frame_size
                end_idx = start_idx + frame_size
                
                if end_idx <= min_length:
                    clean_frame = clean_samples[start_idx:end_idx]
                    noise_frame = noise_samples[start_idx:end_idx]
                    
                    features = create_87d_features(clean_frame, noise_frame)
                    all_features.append(features)
            
            # 每处理10个文件对显示进度
            if processed_count % 10 == 0:
                print(f"  已生成 {len(all_features)} 个特征向量")
    
    if all_features:
        print(f"\n总共生成了 {len(all_features)} 个87维特征向量")
        
        # 保存特征
        with open(output_file, 'wb') as f:
            for feature_vector in all_features:
                for value in feature_vector:
                    f.write(struct.pack('<f', value))
        
        file_size = os.path.getsize(output_file)
        print(f"特征文件已保存: {output_file}")
        print(f"文件大小: {file_size / (1024*1024):.2f} MB")
        print(f"特征向量数量: {len(all_features)}")
        print(f"特征维度: 87")
        
        return True
    else:
        print("错误: 没有生成任何特征")
        return False

def main():
    clean_dir = "data/clean_PCM_data"
    noise_dir = "data/wind_PCM_data"
    output_dir = "training_features"
    
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    output_file = os.path.join(output_dir, "large_features.f32")
    
    print("开始批量处理所有PCM文件...")
    print("这将生成一个大型训练数据集")
    
    # 处理更多文件以生成更大的数据集
    success = process_all_pcm_files(clean_dir, noise_dir, output_file, max_files=100)
    
    if success:
        print(f"\n✅ 成功生成大型训练数据集！")
        print(f"\n可以使用以下命令开始训练:")
        print(f"python torch/rnnoise/train_rnnoise.py {output_file} training_output")
        print(f"\n或者使用PyTorch训练:")
        print(f"cd torch/rnnoise")
        print(f"python train_rnnoise.py ../../{output_file} ../../training_output")
    else:
        print("❌ 特征生成失败")

if __name__ == "__main__":
    main()
