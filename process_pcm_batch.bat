@echo off
echo 批量处理PCM文件生成RNNoise训练特征
echo.

REM 创建输出目录
if not exist "training_features" mkdir "training_features"

REM 设置文件路径
set CLEAN_DIR=data\clean_PCM_data
set WIND_DIR=data\wind_PCM_data
set OUTPUT_DIR=training_features

REM 检查目录是否存在
if not exist "%CLEAN_DIR%" (
    echo 错误: 找不到干净语音目录 %CLEAN_DIR%
    pause
    exit /b 1
)

if not exist "%WIND_DIR%" (
    echo 错误: 找不到风噪声目录 %WIND_DIR%
    pause
    exit /b 1
)

REM 检查是否有编译好的dump_features程序
if not exist "src\dump_features.exe" (
    echo 正在编译dump_features程序...
    cd src
    gcc -DTRAINING=1 -Wall -W -O3 -g -I../include dump_features.c denoise.c kiss_fft.c pitch.c celt_lpc.c -o dump_features.exe -lm
    if errorlevel 1 (
        echo 编译失败，尝试使用现有的denoise_training程序
        cd ..
        goto use_denoise_training
    )
    cd ..
    echo 编译成功！
)

REM 获取一些PCM文件进行测试
echo 开始处理PCM文件...

REM 使用dump_features处理文件
echo 测试dump_features程序...
src\dump_features.exe "%CLEAN_DIR%\000_001.pcm" "%WIND_DIR%\000_001.pcm" "%WIND_DIR%\000_002.pcm" "%OUTPUT_DIR%\test_features.f32" 50

if errorlevel 1 (
    echo dump_features执行失败，尝试其他方法
    goto use_denoise_training
)

echo 成功生成测试特征文件！
goto check_output

:use_denoise_training
echo 尝试使用denoise_training程序...
REM 这里可能需要不同的参数格式
echo 注意：denoise_training的用法可能不同，需要进一步调试

:check_output
if exist "%OUTPUT_DIR%\test_features.f32" (
    echo.
    echo 成功生成特征文件：%OUTPUT_DIR%\test_features.f32
    for %%F in ("%OUTPUT_DIR%\test_features.f32") do echo 文件大小：%%~zF 字节
    
    REM 计算特征向量数量（假设87维，每个float32是4字节）
    for %%F in ("%OUTPUT_DIR%\test_features.f32") do set /a FEATURE_COUNT=%%~zF/(87*4)
    echo 估计特征向量数量：%FEATURE_COUNT%
    echo 特征维度：87
    
    echo.
    echo 可以使用以下命令开始训练：
    echo python torch/rnnoise/train_rnnoise.py %OUTPUT_DIR%\test_features.f32 training_output
) else (
    echo 错误：没有生成特征文件
)

echo.
echo 处理完成！
pause
