#!/usr/bin/env python3
"""
批量处理PCM文件生成RNNoise训练特征的脚本
使用Windows PowerShell命令格式
"""

import os
import subprocess
import random
import sys
from pathlib import Path

def get_pcm_files(directory):
    """获取目录中的所有PCM文件"""
    pcm_files = []
    for file in Path(directory).glob("*.pcm"):
        pcm_files.append(str(file.absolute()))
    return sorted(pcm_files)

def run_denoise_training(speech_file, noise_file, fg_noise_file, output_file, count=100):
    """运行denoise_training命令"""
    # 使用完整路径和.exe扩展名
    cmd = [
        "src\\denoise_training.exe",
        speech_file,
        noise_file,
        output_file
    ]

    print(f"运行命令: {' '.join(cmd)}")
    try:
        # 在根目录运行，不使用shell=True
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"成功生成: {output_file}")
            return True
        else:
            print(f"错误: {result.stderr}")
            print(f"输出: {result.stdout}")
            return False
    except Exception as e:
        print(f"执行错误: {e}")
        return False

def main():
    # 设置路径
    clean_dir = "data/clean_PCM_data"
    wind_dir = "data/wind_PCM_data"
    output_dir = "training_features"

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 获取PCM文件列表
    clean_files = get_pcm_files(clean_dir)
    wind_files = get_pcm_files(wind_dir)

    print(f"找到 {len(clean_files)} 个干净语音文件")
    print(f"找到 {len(wind_files)} 个风噪声文件")

    if not clean_files or not wind_files:
        print("错误: 没有找到PCM文件")
        return

    # 先测试一个文件对
    print("\n开始测试处理...")
    speech_file = clean_files[0]
    noise_file = wind_files[0]
    fg_noise_file = wind_files[1] if len(wind_files) > 1 else wind_files[0]

    output_file = f"{output_dir}/test_features.f32"

    print(f"测试文件:")
    print(f"语音文件: {speech_file}")
    print(f"背景噪声: {noise_file}")
    print(f"输出文件: {output_file}")

    # 尝试运行命令
    success = run_denoise_training(speech_file, noise_file, fg_noise_file, output_file)

    if success:
        print("测试成功！开始批量处理...")
        # 如果测试成功，继续处理更多文件
        feature_files = [output_file]
        batch_size = min(10, len(clean_files), len(wind_files))  # 限制批次大小

        for i in range(1, batch_size):
            speech_file = clean_files[i % len(clean_files)]
            noise_file = wind_files[i % len(wind_files)]
            fg_noise_file = wind_files[(i+1) % len(wind_files)]

            output_file = f"{output_dir}/features_{i:03d}.f32"

            print(f"\n处理批次 {i+1}/{batch_size}")
            if run_denoise_training(speech_file, noise_file, fg_noise_file, output_file):
                feature_files.append(output_file)
    else:
        print("测试失败，请检查命令格式")
        return

    # 合并所有特征文件
    if 'feature_files' in locals() and feature_files:
        if len(feature_files) > 1:
            print(f"\n合并 {len(feature_files)} 个特征文件...")
            final_output = f"{output_dir}/combined_features.f32"

            with open(final_output, "wb") as outf:
                for feature_file in feature_files:
                    if os.path.exists(feature_file):
                        with open(feature_file, "rb") as inf:
                            outf.write(inf.read())
                        # 删除临时文件
                        os.remove(feature_file)

            print(f"最终特征文件: {final_output}")
            print(f"文件大小: {os.path.getsize(final_output) / (1024*1024):.2f} MB")
        elif len(feature_files) == 1:
            final_output = feature_files[0]
            print(f"特征文件: {final_output}")
            print(f"文件大小: {os.path.getsize(final_output) / (1024*1024):.2f} MB")

        # 检查特征维度
        if 'final_output' in locals() and os.path.exists(final_output):
            file_size = os.path.getsize(final_output)
            # 每个特征向量87维，每个float32是4字节
            feature_count = file_size // (87 * 4)
            print(f"特征向量数量: {feature_count}")
            print(f"特征维度: 87 (每个特征向量)")

            # 显示训练命令
            print(f"\n可以使用以下命令开始训练:")
            print(f"python3 torch/rnnoise/train_rnnoise.py {final_output} training_output")
    else:
        print("错误: 没有成功生成任何特征文件")

if __name__ == "__main__":
    main()
