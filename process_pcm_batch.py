#!/usr/bin/env python3
"""
批量处理PCM文件生成RNNoise训练特征的脚本
"""

import os
import subprocess
import random
import sys
from pathlib import Path

def get_pcm_files(directory):
    """获取目录中的所有PCM文件"""
    pcm_files = []
    for file in Path(directory).glob("*.pcm"):
        pcm_files.append(str(file))
    return sorted(pcm_files)

def run_dump_features(speech_file, noise_file, fg_noise_file, output_file, count=100):
    """运行dump_features命令"""
    cmd = [
        "./dump_features",
        speech_file,
        noise_file, 
        fg_noise_file,
        output_file,
        str(count)
    ]
    
    print(f"运行命令: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, cwd="src", capture_output=True, text=True)
        if result.returncode == 0:
            print(f"成功生成: {output_file}")
            return True
        else:
            print(f"错误: {result.stderr}")
            return False
    except Exception as e:
        print(f"执行错误: {e}")
        return False

def main():
    # 设置路径
    clean_dir = "data/clean_PCM_data"
    wind_dir = "data/wind_PCM_data"
    output_dir = "training_features"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取PCM文件列表
    clean_files = get_pcm_files(clean_dir)
    wind_files = get_pcm_files(wind_dir)
    
    print(f"找到 {len(clean_files)} 个干净语音文件")
    print(f"找到 {len(wind_files)} 个风噪声文件")
    
    if not clean_files or not wind_files:
        print("错误: 没有找到PCM文件")
        return
    
    # 生成训练特征
    feature_files = []
    batch_size = 10  # 每批处理的文件对数
    
    for i in range(min(len(clean_files), len(wind_files), batch_size)):
        # 随机选择文件对
        speech_file = random.choice(clean_files)
        noise_file = random.choice(wind_files)
        fg_noise_file = random.choice(wind_files)  # 前景噪声也使用风噪声
        
        output_file = f"{output_dir}/features_{i:03d}.f32"
        
        print(f"\n处理批次 {i+1}/{batch_size}")
        print(f"语音文件: {speech_file}")
        print(f"背景噪声: {noise_file}")
        print(f"前景噪声: {fg_noise_file}")
        
        if run_dump_features(speech_file, noise_file, fg_noise_file, output_file, count=50):
            feature_files.append(output_file)
    
    # 合并所有特征文件
    if feature_files:
        print(f"\n合并 {len(feature_files)} 个特征文件...")
        final_output = f"{output_dir}/combined_features.f32"
        
        with open(final_output, "wb") as outf:
            for feature_file in feature_files:
                if os.path.exists(feature_file):
                    with open(feature_file, "rb") as inf:
                        outf.write(inf.read())
                    # 删除临时文件
                    os.remove(feature_file)
        
        print(f"最终特征文件: {final_output}")
        print(f"文件大小: {os.path.getsize(final_output) / (1024*1024):.2f} MB")
        
        # 显示训练命令
        print(f"\n可以使用以下命令开始训练:")
        print(f"python3 torch/rnnoise/train_rnnoise.py {final_output} training_output")
    else:
        print("错误: 没有成功生成任何特征文件")

if __name__ == "__main__":
    main()
