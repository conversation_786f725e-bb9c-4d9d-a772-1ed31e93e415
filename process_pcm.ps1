# PowerShell脚本：批量处理PCM文件生成RNNoise训练特征
Write-Host "批量处理PCM文件生成RNNoise训练特征" -ForegroundColor Green

# 创建输出目录
$outputDir = "training_features"
if (!(Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir | Out-Null
    Write-Host "创建输出目录: $outputDir" -ForegroundColor Yellow
}

# 设置文件路径
$cleanDir = "data\clean_PCM_data"
$windDir = "data\wind_PCM_data"

# 检查目录是否存在
if (!(Test-Path $cleanDir)) {
    Write-Host "错误: 找不到干净语音目录 $cleanDir" -ForegroundColor Red
    return
}

if (!(Test-Path $windDir)) {
    Write-Host "错误: 找不到风噪声目录 $windDir" -ForegroundColor Red
    return
}

# 获取PCM文件列表
$cleanFiles = Get-ChildItem -Path $cleanDir -Filter "*.pcm" | Select-Object -First 10
$windFiles = Get-ChildItem -Path $windDir -Filter "*.pcm" | Select-Object -First 10

Write-Host "找到 $($cleanFiles.Count) 个干净语音文件（取前10个）" -ForegroundColor Cyan
Write-Host "找到 $($windFiles.Count) 个风噪声文件（取前10个）" -ForegroundColor Cyan

if ($cleanFiles.Count -eq 0 -or $windFiles.Count -eq 0) {
    Write-Host "错误: 没有找到PCM文件" -ForegroundColor Red
    return
}

# 检查是否有可用的程序
$dumpFeaturesPath = "src\dump_features.exe"
$denoiseTrainingPath = "src\denoise_training.exe"

if (Test-Path $dumpFeaturesPath) {
    Write-Host "使用 dump_features.exe 程序" -ForegroundColor Green
    $programPath = $dumpFeaturesPath
    $usesDumpFeatures = $true
} elseif (Test-Path $denoiseTrainingPath) {
    Write-Host "使用 denoise_training.exe 程序" -ForegroundColor Green
    $programPath = $denoiseTrainingPath
    $usesDumpFeatures = $false
} else {
    Write-Host "错误: 找不到可用的处理程序" -ForegroundColor Red
    return
}

# 处理文件
$successCount = 0
$totalAttempts = [Math]::Min($cleanFiles.Count, $windFiles.Count, 5)  # 限制为5个文件

for ($i = 0; $i -lt $totalAttempts; $i++) {
    $speechFile = $cleanFiles[$i].FullName
    $noiseFile = $windFiles[$i].FullName
    $fgNoiseFile = $windFiles[($i + 1) % $windFiles.Count].FullName
    $outputFile = "$outputDir\features_$($i.ToString('000')).f32"
    
    Write-Host "`n处理批次 $($i + 1)/$totalAttempts" -ForegroundColor Yellow
    Write-Host "语音文件: $($cleanFiles[$i].Name)"
    Write-Host "背景噪声: $($windFiles[$i].Name)"
    Write-Host "输出文件: $outputFile"
    
    try {
        if ($usesDumpFeatures) {
            # dump_features 需要5个参数: speech noise fg_noise output count
            $arguments = @($speechFile, $noiseFile, $fgNoiseFile, $outputFile, "50")
        } else {
            # denoise_training 可能需要不同的参数
            $arguments = @($speechFile, $noiseFile, $outputFile)
        }
        
        Write-Host "运行命令: $programPath $($arguments -join ' ')"
        
        $process = Start-Process -FilePath $programPath -ArgumentList $arguments -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0 -and (Test-Path $outputFile)) {
            $fileSize = (Get-Item $outputFile).Length
            Write-Host "成功生成: $outputFile (大小: $fileSize 字节)" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "失败: 退出代码 $($process.ExitCode)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "执行错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 合并特征文件
if ($successCount -gt 0) {
    Write-Host "`n成功处理了 $successCount 个文件" -ForegroundColor Green
    
    $featureFiles = Get-ChildItem -Path $outputDir -Filter "features_*.f32"
    if ($featureFiles.Count -gt 1) {
        Write-Host "合并 $($featureFiles.Count) 个特征文件..." -ForegroundColor Yellow
        $finalOutput = "$outputDir\combined_features.f32"
        
        # 合并文件
        $combinedContent = @()
        foreach ($file in $featureFiles) {
            $content = [System.IO.File]::ReadAllBytes($file.FullName)
            $combinedContent += $content
        }
        [System.IO.File]::WriteAllBytes($finalOutput, $combinedContent)
        
        # 删除临时文件
        $featureFiles | Remove-Item
        
        $finalSize = (Get-Item $finalOutput).Length
        Write-Host "最终特征文件: $finalOutput" -ForegroundColor Green
        Write-Host "文件大小: $([Math]::Round($finalSize / 1MB, 2)) MB" -ForegroundColor Green
        
        # 计算特征向量数量（87维，每个float32是4字节）
        $featureCount = [Math]::Floor($finalSize / (87 * 4))
        Write-Host "估计特征向量数量: $featureCount" -ForegroundColor Cyan
        Write-Host "特征维度: 87" -ForegroundColor Cyan
        
        Write-Host "`n可以使用以下命令开始训练:" -ForegroundColor Yellow
        Write-Host "python torch/rnnoise/train_rnnoise.py $finalOutput training_output" -ForegroundColor White
    } elseif ($featureFiles.Count -eq 1) {
        $singleFile = $featureFiles[0]
        $fileSize = $singleFile.Length
        Write-Host "特征文件: $($singleFile.FullName)" -ForegroundColor Green
        Write-Host "文件大小: $([Math]::Round($fileSize / 1MB, 2)) MB" -ForegroundColor Green
        
        $featureCount = [Math]::Floor($fileSize / (87 * 4))
        Write-Host "估计特征向量数量: $featureCount" -ForegroundColor Cyan
        Write-Host "特征维度: 87" -ForegroundColor Cyan
    }
} else {
    Write-Host "错误: 没有成功生成任何特征文件" -ForegroundColor Red
}

Write-Host "`n处理完成！" -ForegroundColor Green
