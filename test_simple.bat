@echo off
echo Testing PCM file processing...

REM Create output directory
if not exist "training_features" mkdir "training_features"

REM Test with denoise_training (without .exe)
echo Testing denoise_training...
"D:\RNNoise\rnnoise-main\src\denoise_training" "data\clean_PCM_data\000_001.pcm" "data\wind_PCM_data\000_001.pcm" "training_features\test_output.f32"

echo Exit code: %ERRORLEVEL%

if exist "training_features\test_output.f32" (
    echo Success! Generated file: training_features\test_output.f32
    for %%F in ("training_features\test_output.f32") do echo File size: %%~zF bytes
) else (
    echo No output file generated
)

echo Test complete
pause
