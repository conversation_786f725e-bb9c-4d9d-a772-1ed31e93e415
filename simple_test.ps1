# 简单测试脚本
Write-Host "开始测试PCM文件处理..." -ForegroundColor Green

# 创建输出目录
if (!(Test-Path "training_features")) {
    New-Item -ItemType Directory -Path "training_features"
    Write-Host "创建输出目录: training_features"
}

# 获取第一个PCM文件
$cleanFile = Get-ChildItem "data\clean_PCM_data\*.pcm" | Select-Object -First 1
$windFile = Get-ChildItem "data\wind_PCM_data\*.pcm" | Select-Object -First 1

if ($cleanFile -and $windFile) {
    Write-Host "使用文件:"
    Write-Host "  语音: $($cleanFile.Name)"
    Write-Host "  噪声: $($windFile.Name)"
    
    # 检查可用程序
    if (Test-Path "src\denoise_training.exe") {
        Write-Host "尝试使用 denoise_training.exe..."
        $outputFile = "training_features\test_output.f32"
        
        try {
            # 尝试不同的参数组合
            Write-Host "测试命令: src\denoise_training.exe $($cleanFile.FullName) $($windFile.FullName) $outputFile"
            
            $process = Start-Process -FilePath "src\denoise_training.exe" -ArgumentList @($cleanFile.FullName, $windFile.FullName, $outputFile) -Wait -PassThru -NoNewWindow
            
            Write-Host "进程退出代码: $($process.ExitCode)"
            
            if (Test-Path $outputFile) {
                $size = (Get-Item $outputFile).Length
                Write-Host "成功生成文件: $outputFile (大小: $size 字节)" -ForegroundColor Green
                
                # 计算特征数量
                $featureCount = [Math]::Floor($size / (87 * 4))
                Write-Host "估计特征向量数量: $featureCount (87维)" -ForegroundColor Cyan
            } else {
                Write-Host "未生成输出文件" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "执行错误: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "找不到 denoise_training.exe" -ForegroundColor Red
    }
} else {
    Write-Host "找不到PCM文件" -ForegroundColor Red
}

Write-Host "测试完成"
