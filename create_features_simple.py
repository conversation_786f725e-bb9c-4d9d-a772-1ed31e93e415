#!/usr/bin/env python3
"""
简化版本的特征生成脚本，不依赖numpy
直接处理PCM文件生成87维特征
"""

import os
import struct
import math
import random

def read_pcm_file(filename):
    """读取PCM文件并返回样本列表"""
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        # 假设是16位PCM，小端序
        samples = []
        for i in range(0, len(data), 2):
            if i + 1 < len(data):
                # 读取16位整数
                sample = struct.unpack('<h', data[i:i+2])[0]
                # 归一化到[-1, 1]
                normalized = sample / 32768.0
                samples.append(normalized)
        
        return samples
    except Exception as e:
        print(f"读取PCM文件错误 {filename}: {e}")
        return None

def compute_energy(samples):
    """计算信号能量"""
    energy = 0.0
    for sample in samples:
        energy += sample * sample
    return energy / len(samples) if samples else 0.0

def compute_zero_crossings(samples):
    """计算过零率"""
    crossings = 0
    for i in range(1, len(samples)):
        if (samples[i] >= 0) != (samples[i-1] >= 0):
            crossings += 1
    return crossings / len(samples) if samples else 0.0

def simple_fft_magnitude(samples, num_bands=22):
    """简化的频谱分析，分成指定数量的频带"""
    # 这是一个非常简化的频谱分析
    # 将信号分成频带并计算每个频带的能量
    
    frame_size = len(samples)
    band_size = frame_size // num_bands
    band_energies = []
    
    for band in range(num_bands):
        start_idx = band * band_size
        end_idx = min((band + 1) * band_size, frame_size)
        
        # 计算该频带的能量
        band_energy = 0.0
        for i in range(start_idx, end_idx):
            band_energy += samples[i] * samples[i]
        
        # 添加一些频率相关的权重（模拟FFT效果）
        freq_weight = 1.0 + 0.1 * math.sin(band * math.pi / num_bands)
        band_energy *= freq_weight
        
        band_energies.append(band_energy)
    
    return band_energies

def create_87d_features(clean_samples, noise_samples):
    """从干净语音和噪声样本创建87维特征向量"""
    
    # 确保两个信号长度一致
    min_len = min(len(clean_samples), len(noise_samples))
    clean_samples = clean_samples[:min_len]
    noise_samples = noise_samples[:min_len]
    
    # 混合信号（添加噪声）
    noise_level = 0.3
    mixed_samples = []
    for i in range(min_len):
        mixed_samples.append(clean_samples[i] + noise_level * noise_samples[i])
    
    # 计算基本特征
    clean_energy = compute_energy(clean_samples)
    mixed_energy = compute_energy(mixed_samples)
    noise_energy = compute_energy(noise_samples)
    
    # 计算频带能量
    mixed_bands = simple_fft_magnitude(mixed_samples, 22)
    clean_bands = simple_fft_magnitude(clean_samples, 22)
    
    # 构造87维特征向量
    features = [0.0] * 87
    
    # 前22维：混合信号的频带能量（对数）
    for i in range(22):
        log_energy = math.log(mixed_bands[i] + 1e-10)
        features[i] = log_energy
    
    # 接下来20维：上下文和导数特征（简化）
    for i in range(20):
        if i < 22:
            # 频带能量的简单导数
            if i > 0:
                features[22 + i] = features[i] - features[i-1]
            else:
                features[22 + i] = features[i]
        else:
            # 其他特征（能量比、过零率等）
            if i == 18:
                features[22 + i] = mixed_energy / (clean_energy + 1e-10)
            elif i == 19:
                features[22 + i] = compute_zero_crossings(mixed_samples)
            else:
                features[22 + i] = random.uniform(-0.1, 0.1)
    
    # 中间22维：目标增益（干净信号相对于混合信号的增益）
    for i in range(22):
        gain = math.sqrt((clean_bands[i] + 1e-10) / (mixed_bands[i] + 1e-10))
        gain = min(gain, 2.0)  # 限制增益
        features[42 + i] = gain
    
    # 接下来22维：噪声特征
    noise_bands = simple_fft_magnitude(noise_samples, 22)
    for i in range(22):
        log_noise = math.log(noise_bands[i] + 1e-10)
        features[64 + i] = log_noise
    
    # 最后1维：VAD（语音活动检测）
    vad = 1.0 if clean_energy > 0.01 else 0.0
    features[86] = vad
    
    return features

def process_pcm_files(clean_dir, noise_dir, output_file, max_files=10):
    """处理PCM文件并生成特征"""
    
    # 获取文件列表
    clean_files = []
    noise_files = []
    
    for filename in os.listdir(clean_dir):
        if filename.endswith('.pcm'):
            clean_files.append(os.path.join(clean_dir, filename))
    
    for filename in os.listdir(noise_dir):
        if filename.endswith('.pcm'):
            noise_files.append(os.path.join(noise_dir, filename))
    
    clean_files.sort()
    noise_files.sort()
    
    print(f"找到 {len(clean_files)} 个干净语音文件")
    print(f"找到 {len(noise_files)} 个噪声文件")
    
    if not clean_files or not noise_files:
        print("错误: 没有找到PCM文件")
        return False
    
    # 处理文件
    all_features = []
    frame_size = 480  # 10ms at 48kHz
    
    for i in range(min(len(clean_files), len(noise_files), max_files)):
        clean_file = clean_files[i]
        noise_file = noise_files[i]
        
        print(f"处理文件对 {i+1}/{max_files}: {os.path.basename(clean_file)} + {os.path.basename(noise_file)}")
        
        # 读取音频
        clean_samples = read_pcm_file(clean_file)
        noise_samples = read_pcm_file(noise_file)
        
        if clean_samples is None or noise_samples is None:
            continue
        
        # 处理多个帧
        frames_per_file = 50  # 每个文件处理50帧
        min_length = min(len(clean_samples), len(noise_samples))
        
        for frame_idx in range(frames_per_file):
            start_idx = frame_idx * frame_size
            end_idx = start_idx + frame_size
            
            if end_idx <= min_length:
                clean_frame = clean_samples[start_idx:end_idx]
                noise_frame = noise_samples[start_idx:end_idx]
                
                features = create_87d_features(clean_frame, noise_frame)
                all_features.append(features)
    
    if all_features:
        # 保存特征到.f32文件
        print(f"生成了 {len(all_features)} 个87维特征向量")
        
        with open(output_file, 'wb') as f:
            for feature_vector in all_features:
                for value in feature_vector:
                    # 写入float32
                    f.write(struct.pack('<f', value))
        
        file_size = os.path.getsize(output_file)
        print(f"特征文件已保存: {output_file}")
        print(f"文件大小: {file_size / (1024*1024):.2f} MB")
        print(f"特征向量数量: {len(all_features)}")
        print(f"特征维度: 87")
        
        return True
    else:
        print("错误: 没有生成任何特征")
        return False

def main():
    # 设置路径
    clean_dir = "data/clean_PCM_data"
    noise_dir = "data/wind_PCM_data"
    output_dir = "training_features"
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    output_file = os.path.join(output_dir, "simple_features.f32")
    
    print("开始处理PCM文件生成87维特征...")
    success = process_pcm_files(clean_dir, noise_dir, output_file)
    
    if success:
        print(f"\n可以使用以下命令开始训练:")
        print(f"python torch/rnnoise/train_rnnoise.py {output_file} training_output")
    else:
        print("特征生成失败")

if __name__ == "__main__":
    main()
